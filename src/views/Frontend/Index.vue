<template>
  <div class="frontend-page">
    <!-- 页面头部 -->
    <header class="frontend-header">
      <div class="header-container">
        <div class="logo-section">
          <Icon icon="ep:data-analysis" class="logo-icon" />
          <h1 class="site-title">数据查询平台</h1>
        </div>
        <div class="header-actions">
          <el-button 
            v-if="!isLoggedIn" 
            type="primary" 
            @click="showLoginDialog = true"
            class="login-btn"
          >
            <Icon icon="ep:user" class="btn-icon" />
            登录
          </el-button>
          <div v-else class="user-info">
            <el-dropdown @command="handleUserCommand">
              <span class="user-dropdown">
                <el-avatar :size="32" :src="userInfo.avatar" class="user-avatar">
                  <Icon icon="ep:user" />
                </el-avatar>
                <span class="username">{{ userInfo.nickname || userInfo.username }}</span>
                <Icon icon="ep:arrow-down" class="dropdown-icon" />
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="logout" divided>
                    <Icon icon="ep:switch-button" />
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="frontend-main">
      <div class="main-container">
        <!-- 查询区域 - 复用现有的查询功能 -->
        <section class="query-section">
          <div class="query-container">
            <!-- 搜索工作栏 -->
            <div class="search-container">
              <el-form
                class="search-form"
                :model="queryParams"
                ref="queryFormRef"
                :inline="true"
                label-width="80px"
              >
                <div class="form-row">
                  <el-form-item label="查询数据" prop="value" class="form-item-custom">
                    <el-input
                      v-model="queryParams.value"
                      placeholder="请输入精确结果进行查询"
                      clearable
                      @keyup.enter="handleQuery"
                      class="search-input"
                      prefix-icon="Search"
                    />
                  </el-form-item>
                  <el-button type="primary" @click="handleQuery" class="search-btn">
                    <Icon icon="ep:search" class="btn-icon" />
                    搜索查询
                  </el-button>
                </div>
                <div class="form-row">
                  <el-form-item>
                    <el-radio-group v-model="queryParams.type" @change="handleTypeChange">
                      <el-radio-button label="1">手机号</el-radio-button>
                      <el-radio-button label="2">UPI</el-radio-button>
                      <el-radio-button label="3">银行卡</el-radio-button>
                    </el-radio-group>
                  </el-form-item>
                </div>
              </el-form>
            </div>

            <ContentWrap>
              <!-- 已登录用户显示批次列表 -->
              <div v-if="isLoggedIn" class="batch-list-container">
                <!-- 批次列表头部操作区 -->
                <div class="batch-list-header">
                  <div class="batch-list-title">
                    <Icon icon="ep:list" class="title-icon" />
                    <span>批次列表</span>
                  </div>
                  <el-button
                    @click="handleRefreshBatchList"
                    :loading="loading"
                    class="refresh-btn"
                    size="small"
                  >
                    <Icon icon="ep:refresh" class="btn-icon" />
                    刷新
                  </el-button>
                </div>

                <!-- 表格容器，设置固定高度和滚动 -->
                <div class="table-container">
                  <el-table
                    row-key="id"
                    v-loading="loading"
                    :data="list"
                    :stripe="true"
                    :show-overflow-tooltip="true"
                    height="400"
                    style="width: 100%"
                  >
                    <el-table-column label="批次" align="center" prop="id" />
                    <el-table-column label="类型" align="center" prop="type" :formatter="typeFormatter" />
                    <el-table-column label="数量" align="center" prop="batchCount" />
                    <el-table-column label="所属用户" align="center" prop="userId" v-if="false" />
                    <el-table-column label="失败数量" align="center" prop="failCount" v-if="false"/>
                    <el-table-column
                      label="时间"
                      align="center"
                      prop="createTime"
                      :formatter="dateFormatter"
                      width="180px"
                    />
                    <el-table-column label="操作人" align="center" prop="remark" />
                  </el-table>
                </div>

                <!-- 分页 -->
                <div class="pagination-container">
                  <Pagination
                    v-show="total > 0 || list.length > 0"
                    :total="total"
                    v-model:page="queryParams.pageNo"
                    v-model:limit="queryParams.pageSize"
                    @pagination="getList"
                  />
                  <!-- 调试信息 -->
                  <div v-if="isLoggedIn" class="debug-info" style="font-size: 12px; color: #999; margin-top: 10px;">
                    总数: {{ total }}, 当前页: {{ queryParams.pageNo }}, 页面大小: {{ queryParams.pageSize }}, 列表长度: {{ list.length }}
                  </div>
                </div>
              </div>
              <!-- 未登录用户显示欢迎信息 -->
              <div v-else class="welcome-state">
                <div class="welcome-content">
                  <h4 class="welcome-title">欢迎使用数据查询系统</h4>
                  <p class="welcome-text">请在上方输入查询条件，开始您的数据探索之旅</p>
                  <div class="welcome-tips">
                    <div class="tip-item">
                      <Icon icon="ep:info-filled" class="tip-icon" />
                      <span>支持精确结果匹配查询</span>
                    </div>
                    <div class="tip-item">
                      <Icon icon="ep:search" class="tip-icon" />
                      <span>支持手机号、UPI、银行卡查询</span>
                    </div>
                    <div class="tip-item">
                      <Icon icon="ep:upload" class="tip-icon" />
                      <span>数据不存在时可快速入库</span>
                    </div>
                  </div>
                </div>
              </div>
            </ContentWrap>
            <!-- 结果展示区域 -->
          </div>
        </section>
      </div>
    </main>
    <!-- 登录弹窗 -->
    <LoginDialog
      v-model="showLoginDialog"
      @login-success="handleLoginSuccess"
    />

    <!-- 数据未入库弹窗 -->
    <el-dialog
      v-model="showDataNotFoundDialog"
      title="数据未入库"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <div class="data-not-found-content">
        <div class="warning-icon-wrapper">
          <Icon icon="ep:warning" class="warning-icon" />
        </div>
        <div class="dialog-message">
          <p class="message-text">
            查询的数据 "<strong>{{ queryParams.value }}</strong>" 不存在于数据库中
          </p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogCancel" class="cancel-btn">
            <Icon icon="ep:close" class="btn-icon" />
            取消
          </el-button>
          <el-button @click="resetQuery" class="retry-btn">
            <Icon icon="ep:refresh" class="btn-icon" />
            重新查询
          </el-button>
          <el-button
            type="primary"
            @click="handleImportDataFromDialog"
            v-hasPermi="['system:data-info:create']"
            class="import-btn"
          >
            <Icon icon="ep:upload" class="btn-icon" />
            立即入库
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {dateFormatter} from '@/utils/formatTime'
import {DataInfo, DataInfoApi} from '@/api/system/datainfo'
import {useUserStore} from '@/store/modules/user'
import LoginDialog from './components/LoginDialog.vue'
import {DataBatch, DataBatchApi} from '@/api/system/databatch'

/** 前台数据查询页面 */
defineOptions({ name: 'FrontendIndex' })
const list = ref<DataBatch[]>([]) // 列表的数据
const total = ref(0)
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const userStore = useUserStore()

// 用户登录状态
const isLoggedIn = computed(() => userStore.getIsSetUser)
const userInfo = computed(() => userStore.getUser)

// 登录弹窗显示状态
const showLoginDialog = ref(false)

// 数据未入库弹窗显示状态
const showDataNotFoundDialog = ref(false)

// 查询相关状态
const loading = ref(false) // 查询加载中
const hasSearched = ref(false) // 是否已经搜索过
const dataExists = ref(false) // 数据是否存在
const existingData = ref<DataInfo | null>(null) // 已存在的数据
const queryParams = reactive({
  value: undefined, // 只保留结果查询，支持精确匹配
  type: "1",
  pageNo: 1,
  pageSize: 10
})
const queryFormRef = ref() // 搜索的表单

/** 查询数据是否存在 */
const checkDataExists = async () => {
  loading.value = true
  try {
    // 如果没有输入查询条件，不进行查询
    if (!queryParams.value?.trim()) {
      return
    }

    // 调用API进行精确查询
    const data = await DataInfoApi.getAnoPage({
      value: queryParams.value.trim(),
      pageNo: 1,
      pageSize: 1
    })

    hasSearched.value = true

    if (data.list && data.list.length > 0) {
      // 数据存在
      dataExists.value = true
      existingData.value = data.list[0]
    } else {
      // 数据不存在，显示弹窗
      dataExists.value = false
      existingData.value = null
      showDataNotFoundDialog.value = true
    }
  } catch (error) {
    console.error('查询失败:', error)
    message.error('查询失败，请稍后重试')
    dataExists.value = false
    existingData.value = null
  } finally {
    loading.value = false
  }
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    console.log('查询参数:', queryParams)
    const data = await DataBatchApi.getDataBatchPage(queryParams)
    console.log('查询结果:', data)
    list.value = data.list || []
    total.value = data.total || 0
    console.log('设置后的total:', total.value, 'list长度:', list.value.length)
  } catch (error) {
    console.error('查询批次列表失败:', error)
    message.error('查询批次列表失败，请稍后重试')
    list.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  if (!queryParams.value?.trim()) {
    message.warning('请输入要查询的结果')
    return
  }
  checkDataExists()
}

/** 处理类型切换 */
const handleTypeChange = () => {
  // 切换类型时刷新批次列表（仅在已登录时）
  if (isLoggedIn.value) {
    getList()
  }
}

/** 刷新批次列表 */
const handleRefreshBatchList = () => {
  if (isLoggedIn.value) {
    getList()
  }
}



// 数据格式验证正则表达式
const PHONE_REGEX = /^\d{10,15}$/;
const UPI_REGEX = /@/;
const BANK_CARD_REGEX = /^\d{6,}$/;

/** 格式校验函数 */
const validateFormat = (value: string, type: string): boolean => {
  switch (type) {
    case '1':
      return PHONE_REGEX.test(value);
    case '2':
      return UPI_REGEX.test(value) && value.length > 0;
    case '3':
      return BANK_CARD_REGEX.test(value);
    default:
      return false;
  }
};

/** 入库操作 */
const handleImportData = async () => {
  if (!queryParams.value?.trim()) {
    message.warning('请输入要入库的数据')
    return
  }

  const inputValue = queryParams.value.trim()
  const dataType = queryParams.type

  // 进行格式验证
  if (!validateFormat(inputValue, dataType)) {
    let formatTip = ''
    switch (dataType) {
      case '1':
        formatTip = '手机号格式不正确，应为10-15位数字'
        break
      case '2':
        formatTip = 'UPI格式不正确，应包含@符号'
        break
      case '3':
        formatTip = '银行卡格式不正确，应为6位以上数字'
        break
    }
    message.error(`入库失败，${formatTip}`)
    return
  }

  try {
    // 调用创建数据API
    let res = await DataInfoApi.createDataInfo({
      value: inputValue,
      type: parseInt(dataType), // 使用选择的类型
    } as DataInfo)

    if (!res.batchId) {
      message.error(`入库失败，数据${inputValue}不符合数据格式要求`)
      return
    }

    message.success('数据入库成功')

    // 重新查询以更新状态
    await checkDataExists()
  } catch (error) {
    console.error('入库失败:', error)
    message.error('入库失败，请稍后重试')
  } finally {

  }
}

/** 从弹窗中进行入库操作 */
const handleImportDataFromDialog = async () => {
  try {
    await handleImportData()
    // 入库成功后关闭弹窗
    showDataNotFoundDialog.value = false

    if (isLoggedIn.value) {
      // 立即刷新一次
      getList()

      // 每5秒刷新一次，总共刷新3次
      let refreshCount = 0
      const maxRefreshCount = 3

      const refreshInterval = setInterval(() => {
        refreshCount++
        if (isLoggedIn.value) {
          getList()
        }

        if (refreshCount >= maxRefreshCount) {
          clearInterval(refreshInterval)
        }
      }, 5000) // 5秒 = 5000毫秒
    }
  } catch (error) {
    console.error('导入数据失败:', error)
    // 可以在这里添加错误处理逻辑
  }
}


/** 处理弹窗取消操作 */
const handleDialogCancel = () => {
  showDataNotFoundDialog.value = false
}

/** 重置查询（重写以关闭弹窗） */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  hasSearched.value = false
  dataExists.value = false
  existingData.value = null
  showDataNotFoundDialog.value = false
}

/** 格式化时间 */
const formatTime = (time: string | Date | undefined) => {
  if (!time) return '未知'
  return dateFormatter(null, null, time)
}

/** 格式化类型 */
const typeFormatter = (row: any, column: any, cellValue: any) => {
  switch (cellValue) {
    case 1:
    case '1':
      return '手机号'
    case 2:
    case '2':
      return 'UPI'
    case 3:
    case '3':
      return '银行卡'
    default:
      return '未知'
  }
}

/** 处理用户下拉菜单命令 */
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/user/profile')
      break
    case 'logout':
      handleLogout()
      break
  }
}

/** 处理登录成功 */
const handleLoginSuccess = () => {
  showLoginDialog.value = false
  // message.success('登录成功')
  // 登录成功后加载批次列表
  getList()
}

/** 处理退出登录 */
const handleLogout = async () => {
  try {
    await userStore.loginOut()
    message.success('退出登录成功')
    // 清空查询结果
    resetQuery()
  } catch (error) {
    console.error('退出登录失败:', error)
    message.error('退出登录失败')
  }
}

/** 初始化 */
onMounted(() => {
  // 只有在用户已登录时才加载批次列表
  if (isLoggedIn.value) {
    getList()
  }
})
</script>
<style scoped lang="scss">
@use './styles/index.scss';
</style>
